"""
项目管理器
管理项目的创建、加载、保存等操作
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from .logger import get_logger


class ProjectManager:
    """项目管理器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化项目管理器
        
        Args:
            config: 应用程序配置
        """
        self.config = config
        self.logger = get_logger(__name__)
        self.current_project = None
        self.project_data = {}
    
    def create_new_project(self, project_name: str, project_path: str) -> bool:
        """
        创建新项目
        
        Args:
            project_name: 项目名称
            project_path: 项目路径
            
        Returns:
            bool: 创建是否成功
        """
        try:
            project_dir = Path(project_path) / project_name
            project_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建项目配置文件
            project_config = {
                'name': project_name,
                'version': '1.0.0',
                'created_at': str(Path().ctime()),
                'description': f'{project_name} - I/O分配项目'
            }
            
            config_file = project_dir / 'project.json'
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(project_config, f, indent=2, ensure_ascii=False)
            
            self.current_project = str(project_dir)
            self.project_data = project_config
            
            self.logger.info(f"新项目创建成功: {project_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建项目失败: {e}")
            return False
    
    def load_project(self, project_path: str) -> bool:
        """
        加载项目
        
        Args:
            project_path: 项目路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            config_file = Path(project_path) / 'project.json'
            if not config_file.exists():
                self.logger.error(f"项目配置文件不存在: {config_file}")
                return False
            
            with open(config_file, 'r', encoding='utf-8') as f:
                self.project_data = json.load(f)
            
            self.current_project = project_path
            self.logger.info(f"项目加载成功: {self.project_data.get('name', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载项目失败: {e}")
            return False
    
    def save_project(self) -> bool:
        """
        保存当前项目
        
        Returns:
            bool: 保存是否成功
        """
        if not self.current_project:
            self.logger.warning("没有当前项目可保存")
            return False
        
        try:
            config_file = Path(self.current_project) / 'project.json'
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.project_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info("项目保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存项目失败: {e}")
            return False
    
    def get_project_info(self) -> Dict[str, Any]:
        """
        获取项目信息
        
        Returns:
            Dict[str, Any]: 项目信息
        """
        return self.project_data.copy()
    
    def is_project_loaded(self) -> bool:
        """
        检查是否有项目已加载
        
        Returns:
            bool: 是否有项目已加载
        """
        return self.current_project is not None
