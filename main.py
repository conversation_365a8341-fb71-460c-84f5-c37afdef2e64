#!/usr/bin/env python3
"""
EWReborn - 自动化I/O点领域分配系统
主程序入口文件

Author: EWReborn Development Team
Version: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# PySide6 imports
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QDir, QTimer
from PySide6.QtGui import QIcon

# 项目模块导入
from core.logger import setup_logger
from utils.config_manager_simple import ConfigManager
from gui.main_window import MainWindow
from gui.splash_screen import AppleSplashScreen


def setup_application():
    """设置应用程序基本配置"""
    # 设置应用程序属性
    QApplication.setApplicationName("EWReborn")
    QApplication.setApplicationVersion("1.0.0")
    QApplication.setOrganizationName("EWReborn Development Team")
    QApplication.setOrganizationDomain("ewreborn.local")
    
    # 设置高DPI支持
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)


def check_dependencies():
    """检查必要的依赖项"""
    required_modules = [
        'pandas', 'openpyxl', 'PySide6'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        error_msg = f"缺少必要的依赖模块: {', '.join(missing_modules)}\n"
        error_msg += "请运行: pip install -r requirements.txt"
        print(error_msg)
        return False
    
    return True


def create_directories():
    """创建必要的目录"""
    directories = [
        'logs',
        'output',
        'temp'
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)


def main():
    """主函数"""
    try:
        # 检查依赖项
        if not check_dependencies():
            sys.exit(1)
        
        # 创建必要目录
        create_directories()
        
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        # 设置应用程序配置
        setup_application()
        
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # 设置日志系统
        setup_logger(config.get('logging', {}))
        logger = logging.getLogger(__name__)

        logger.info("EWReborn应用程序启动")
        
        # 设置应用程序图标（如果存在）
        icon_path = project_root / "resources" / "icons" / "app_icon.ico"
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))

        # 创建并显示启动画面
        splash = AppleSplashScreen()
        splash.show_with_animation()

        # 模拟加载过程
        splash.simulate_loading()

        # 创建主窗口
        try:
            main_window = MainWindow(config)

            # 当启动画面完成时显示主窗口
            def show_main_window():
                splash.close()
                main_window.show()
                logger.info("主窗口显示完成")

            splash.loading_finished.connect(show_main_window)

            logger.info("主窗口创建成功")
            
        except Exception as e:
            logger.error(f"创建主窗口失败: {e}")



            QMessageBox.critical(
                None,
                "启动错误",
                f"无法创建主窗口:\n{str(e)}\n\n请检查配置文件和依赖项。"
            )
            sys.exit(1)
        
        # 运行应用程序
        exit_code = app.exec()
        logger.info(f"应用程序退出，退出码: {exit_code}")
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(0)
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {str(e)}"
        print(error_msg)
        
        # 尝试显示错误对话框
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
