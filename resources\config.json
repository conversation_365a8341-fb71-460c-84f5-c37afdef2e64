{"application": {"name": "EWReborn - I/O点自动分配系统", "version": "1.0.0", "window": {"width": 1400, "height": 900, "min_width": 1000, "min_height": 600}}, "data_paths": {"cabinet_profiles": "data/cabinet_profiles", "wiring_typical": "data/wiring_typical", "iodb": "data/iodb", "pidb": "data/pidb"}, "allocation_settings": {"enable_parttype_matching": true, "enable_detailed_logging": true, "max_allocation_attempts": 1000, "allocation_order": "cable_name_pair_asc"}, "validation_rules": {"tag_uniqueness": true, "cable_pair_validation": true, "cable_attribute_consistency": true}, "logging": {"level": "INFO", "file_path": "logs/ewreborn.log", "max_file_size": "10MB", "backup_count": 5}, "spare_settings": {"default_spare_limit": 2, "etp_spare_limits": {"CPM16-AI3700": 2, "CPM16-AO3700": 2, "CPM16-DI3700": 2, "CPM16-DO3700": 2}, "enable_cable_spare": true, "enable_etp_spare": true, "spare_naming_prefix": "SPARE_", "spare_description": "-"}, "gui": {"theme": "default", "show_progress_details": true, "auto_save_results": true}}